/**
 * 轮询管理器组合式函数
 * 
 * 用于管理AI消息的轮询状态恢复，支持页面刷新和网络中断后的状态恢复
 */
import { ref, onUnmounted } from 'vue';
import { useLogState } from './useLogState.js';

/**
 * 轮询状态枚举
 */
const POLLING_STATUS = {
    IDLE: 'idle',           // 空闲
    POLLING: 'polling',     // 轮询中
    COMPLETED: 'completed', // 已完成
    ERROR: 'error'          // 错误
};

/**
 * 轮询配置
 */
const POLLING_CONFIG = {
    INITIAL_INTERVAL: 500,    // 初始轮询间隔（毫秒）
    MAX_INTERVAL: 5000,       // 最大轮询间隔（毫秒）
    BACKOFF_MULTIPLIER: 1.5,  // 退避乘数
    MAX_RETRIES: 10,          // 最大重试次数
    TIMEOUT: 30000,           // 请求超时时间（毫秒）
    MAX_POLLING_TIME: 30 * 60 * 1000,  // 最大轮询时间（30分钟）
    STALE_DETECTION_TIME: 10 * 60 * 1000,  // 消息过期检测时间（10分钟）
    HEALTH_CHECK_INTERVAL: 5 * 60 * 1000   // 健康检查间隔（5分钟）
};

export function usePollingManager() {
    // 活跃的轮询状态映射 messageId -> pollState
    const activePolls = ref(new Map());
    
    // 获取日志状态管理器
    const logState = useLogState();

    /**
     * 轮询状态对象
     */
    const createPollState = (messageId, options = {}) => ({
        messageId,
        status: POLLING_STATUS.IDLE,
        interval: POLLING_CONFIG.INITIAL_INTERVAL,
        retryCount: 0,
        timerId: null,
        abortController: null,
        lastContent: '',
        lastUpdated: null,
        startTime: Date.now(),           // 轮询开始时间
        lastSuccessTime: Date.now(),     // 最后成功时间
        consecutiveErrors: 0,            // 连续错误次数
        totalRequests: 0,                // 总请求次数
        isStale: false,                  // 是否被标记为过期
        onUpdate: options.onUpdate || (() => {}),
        onComplete: options.onComplete || (() => {}),
        onError: options.onError || (() => {}),
        onStale: options.onStale || (() => {})  // 过期回调
    });

    /**
     * 开始轮询指定消息
     * 
     * @param {number} messageId - 消息ID
     * @param {Object} callbacks - 回调函数
     * @param {Function} callbacks.onUpdate - 内容更新回调
     * @param {Function} callbacks.onComplete - 完成回调
     * @param {Function} callbacks.onError - 错误回调
     */
    const startPolling = (messageId, callbacks = {}) => {
        console.log(`[PollingManager] 开始轮询消息 ${messageId}`);
        
        // 如果已经在轮询，先停止
        if (activePolls.value.has(messageId)) {
            stopPolling(messageId);
        }
        
        // 创建轮询状态
        const pollState = createPollState(messageId, callbacks);
        activePolls.value.set(messageId, pollState);
        
        // 开始轮询
        pollMessage(pollState);
    };

    /**
     * 停止轮询指定消息
     * 
     * @param {number} messageId - 消息ID
     */
    const stopPolling = (messageId) => {
        const pollState = activePolls.value.get(messageId);
        if (!pollState) return;
        
        console.log(`[PollingManager] 停止轮询消息 ${messageId}`);
        
        // 清理定时器
        if (pollState.timerId) {
            clearTimeout(pollState.timerId);
            pollState.timerId = null;
        }
        
        // 取消正在进行的请求
        if (pollState.abortController) {
            pollState.abortController.abort();
            pollState.abortController = null;
        }
        
        // 更新状态
        pollState.status = POLLING_STATUS.IDLE;
        
        // 从活跃轮询中移除
        activePolls.value.delete(messageId);
    };

    /**
     * 停止所有轮询
     */
    const stopAllPolling = () => {
        console.log(`[PollingManager] 停止所有轮询，当前活跃: ${activePolls.value.size}`);
        
        for (const messageId of activePolls.value.keys()) {
            stopPolling(messageId);
        }
    };

    /**
     * 检查轮询是否超时或过期
     *
     * @param {Object} pollState - 轮询状态对象
     * @returns {Object} 检查结果
     */
    const checkPollingHealth = (pollState) => {
        const now = Date.now();
        const pollingDuration = now - pollState.startTime;
        const timeSinceLastSuccess = now - pollState.lastSuccessTime;

        return {
            isTimeout: pollingDuration > POLLING_CONFIG.MAX_POLLING_TIME,
            isStale: timeSinceLastSuccess > POLLING_CONFIG.STALE_DETECTION_TIME,
            pollingDuration,
            timeSinceLastSuccess,
            consecutiveErrors: pollState.consecutiveErrors
        };
    };

    /**
     * 执行单次轮询
     *
     * @param {Object} pollState - 轮询状态对象
     */
    const pollMessage = async (pollState) => {
        if (pollState.status === POLLING_STATUS.COMPLETED) {
            return;
        }

        // 检查轮询健康状态
        const healthCheck = checkPollingHealth(pollState);

        if (healthCheck.isTimeout) {
            console.warn(`[PollingManager] 消息 ${pollState.messageId} 轮询超时 (${Math.round(healthCheck.pollingDuration / 1000)}秒)`);
            pollState.status = POLLING_STATUS.ERROR;
            pollState.onError(new Error(`轮询超时：已等待 ${Math.round(healthCheck.pollingDuration / 60000)} 分钟`));
            stopPolling(pollState.messageId);
            return;
        }

        if (healthCheck.isStale && !pollState.isStale) {
            console.warn(`[PollingManager] 消息 ${pollState.messageId} 可能已过期 (${Math.round(healthCheck.timeSinceLastSuccess / 1000)}秒无响应)`);
            pollState.isStale = true;
            pollState.onStale({
                messageId: pollState.messageId,
                timeSinceLastSuccess: healthCheck.timeSinceLastSuccess,
                suggestion: '消息可能已过期，建议刷新页面或联系管理员'
            });
        }

        pollState.status = POLLING_STATUS.POLLING;
        pollState.abortController = new AbortController();
        pollState.totalRequests++;
        
        try {
            console.log(`[PollingManager] 轮询消息 ${pollState.messageId}，间隔: ${pollState.interval}ms`);
            
            const response = await fetch(`/query/poll/${pollState.messageId}`, {
                method: 'GET',
                signal: pollState.abortController.signal,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // 检查内容是否有更新
            const hasContentUpdate = data.content !== pollState.lastContent;

            // 更新成功统计
            pollState.lastSuccessTime = Date.now();
            pollState.consecutiveErrors = 0;

            if (hasContentUpdate) {
                console.log(`[PollingManager] 消息 ${pollState.messageId} 有新内容，长度: ${data.content.length}`);

                // 重置轮询间隔（有新内容时加快轮询）
                pollState.interval = POLLING_CONFIG.INITIAL_INTERVAL;
                pollState.retryCount = 0;
                pollState.lastContent = data.content;
                pollState.lastUpdated = data.last_updated;
                pollState.isStale = false; // 重置过期状态

                // 调用更新回调
                pollState.onUpdate({
                    content: data.content,
                    logs: data.logs,
                    agent: data.agent,
                    conversationId: data.conversation_id,
                    isComplete: data.is_completed
                });
            } else {
                // 无新内容，增加轮询间隔
                pollState.interval = Math.min(
                    pollState.interval * POLLING_CONFIG.BACKOFF_MULTIPLIER,
                    POLLING_CONFIG.MAX_INTERVAL
                );
            }
            
            // 检查是否已完成
            if (data.is_completed) {
                console.log(`[PollingManager] 消息 ${pollState.messageId} 已完成`);
                
                pollState.status = POLLING_STATUS.COMPLETED;
                
                // 调用完成回调
                pollState.onComplete({
                    content: data.content,
                    logs: data.logs,
                    agent: data.agent,
                    conversationId: data.conversation_id,
                    timeSpend: data.time_spend
                });
                
                // 停止轮询
                stopPolling(pollState.messageId);
                return;
            }
            
            // 继续轮询
            scheduleNextPoll(pollState);
            
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log(`[PollingManager] 轮询消息 ${pollState.messageId} 被取消`);
                return;
            }
            
            console.error(`[PollingManager] 轮询消息 ${pollState.messageId} 失败:`, error);

            pollState.retryCount++;
            pollState.consecutiveErrors++;

            // 检查是否达到最大重试次数或连续错误过多
            const shouldStop = pollState.retryCount >= POLLING_CONFIG.MAX_RETRIES ||
                             pollState.consecutiveErrors >= 20;

            if (shouldStop) {
                console.error(
                    `[PollingManager] 消息 ${pollState.messageId} 轮询失败次数过多，停止轮询 ` +
                    `(重试: ${pollState.retryCount}, 连续错误: ${pollState.consecutiveErrors})`
                );

                pollState.status = POLLING_STATUS.ERROR;

                // 提供更详细的错误信息
                const errorDetails = {
                    originalError: error,
                    retryCount: pollState.retryCount,
                    consecutiveErrors: pollState.consecutiveErrors,
                    totalRequests: pollState.totalRequests,
                    pollingDuration: Date.now() - pollState.startTime,
                    suggestion: '请检查网络连接或刷新页面重试'
                };

                pollState.onError(errorDetails);
                stopPolling(pollState.messageId);
                return;
            }

            // 增加重试间隔（指数退避）
            pollState.interval = Math.min(
                pollState.interval * 2,
                POLLING_CONFIG.MAX_INTERVAL
            );

            // 继续重试
            scheduleNextPoll(pollState);
        }
    };

    /**
     * 安排下次轮询
     * 
     * @param {Object} pollState - 轮询状态对象
     */
    const scheduleNextPoll = (pollState) => {
        if (pollState.status === POLLING_STATUS.COMPLETED) {
            return;
        }
        
        pollState.timerId = setTimeout(() => {
            if (activePolls.value.has(pollState.messageId)) {
                pollMessage(pollState);
            }
        }, pollState.interval);
    };

    /**
     * 获取轮询状态
     * 
     * @param {number} messageId - 消息ID
     * @returns {Object|null} 轮询状态对象
     */
    const getPollingStatus = (messageId) => {
        return activePolls.value.get(messageId) || null;
    };

    /**
     * 检查是否正在轮询
     * 
     * @param {number} messageId - 消息ID
     * @returns {boolean} 是否正在轮询
     */
    const isPolling = (messageId) => {
        const pollState = activePolls.value.get(messageId);
        return pollState && pollState.status === POLLING_STATUS.POLLING;
    };

    /**
     * 获取活跃轮询数量
     * 
     * @returns {number} 活跃轮询数量
     */
    const getActivePollingCount = () => {
        return activePolls.value.size;
    };

    // 组件卸载时清理所有轮询
    onUnmounted(() => {
        stopAllPolling();
    });

    // 监听网络状态变化
    if (typeof window !== 'undefined') {
        const handleOnline = () => {
            console.log('[PollingManager] 网络恢复，重新启动轮询');
            // 网络恢复时，重新启动所有轮询
            for (const pollState of activePolls.value.values()) {
                if (pollState.status === POLLING_STATUS.ERROR) {
                    pollState.status = POLLING_STATUS.IDLE;
                    pollState.retryCount = 0;
                    pollState.interval = POLLING_CONFIG.INITIAL_INTERVAL;
                    pollMessage(pollState);
                }
            }
        };

        const handleOffline = () => {
            console.log('[PollingManager] 网络断开，暂停轮询');
            // 网络断开时，取消所有正在进行的请求
            for (const pollState of activePolls.value.values()) {
                if (pollState.abortController) {
                    pollState.abortController.abort();
                }
                if (pollState.timerId) {
                    clearTimeout(pollState.timerId);
                    pollState.timerId = null;
                }
            }
        };

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        // 清理事件监听器
        onUnmounted(() => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        });
    }

    return {
        // 方法
        startPolling,
        stopPolling,
        stopAllPolling,
        getPollingStatus,
        isPolling,
        getActivePollingCount,
        
        // 状态
        activePolls: activePolls.value,
        
        // 常量
        POLLING_STATUS
    };
}
