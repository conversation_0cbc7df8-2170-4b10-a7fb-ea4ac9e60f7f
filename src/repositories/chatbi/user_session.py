"""
用户Session仓储实现

负责用户session的数据持久化操作
"""

from typing import Optional, List
from datetime import datetime

from src.db.connection import execute_db_query
from src.models.user_session import UserSession
from src.utils.logger import logger


class UserSessionRepository:
    """用户Session仓储类"""
    
    # 注意：表结构创建已移至 init.sql 文件
    # 该仓储类专注于数据操作，不再包含建表逻辑
    
    @staticmethod
    def save(session: UserSession) -> bool:
        """
        保存用户session
        
        Args:
            session: 用户session实例
            
        Returns:
            bool: 是否保存成功
        """
        sql = """
        INSERT INTO `user_session` 
        (`session_id`, `open_id`, `refresh_token`, `access_token`, `access_token_expires_at`, 
         `created_at`, `updated_at`, `last_active_at`, `is_active`)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            session.session_id,
            session.open_id,
            session.refresh_token,
            session.access_token,
            session.access_token_expires_at,
            session.created_at,
            session.updated_at,
            session.last_active_at,
            session.is_active
        )
        
        try:
            execute_db_query(sql, params, commit=True)
            logger.info(f"用户session保存成功: session_id={session.session_id}")
            return True
        except Exception as e:
            logger.exception(f"保存用户session失败: {e}", exc_info=True)
            return False
    
    @staticmethod
    def find_by_session_id(session_id: str) -> Optional[UserSession]:
        """
        根据session_id查找session
        
        Args:
            session_id: session ID
            
        Returns:
            Optional[UserSession]: session实例，不存在时返回None
        """
        sql = """
        SELECT `session_id`, `open_id`, `refresh_token`, `access_token`, 
               `access_token_expires_at`, `created_at`, `updated_at`, 
               `last_active_at`, `is_active`
        FROM `user_session`
        WHERE `session_id` = %s AND `is_active` = 1
        """
        
        try:
            result = execute_db_query(sql, (session_id,), fetch='one')
            if result:
                return UserSession(
                    session_id=result['session_id'],
                    open_id=result['open_id'],
                    refresh_token=result['refresh_token'],
                    access_token=result['access_token'],
                    access_token_expires_at=result['access_token_expires_at'],
                    created_at=result['created_at'],
                    updated_at=result['updated_at'],
                    last_active_at=result['last_active_at'],
                    is_active=bool(result['is_active'])
                )
            return None
        except Exception as e:
            logger.exception(f"查找用户session失败: {e}", exc_info=True)
            return None
    
    @staticmethod
    def find_active_sessions_by_open_id(open_id: str) -> List[UserSession]:
        """
        根据open_id查找活跃的sessions
        
        Args:
            open_id: 用户open_id
            
        Returns:
            List[UserSession]: 活跃的session列表
        """
        sql = """
        SELECT `session_id`, `open_id`, `refresh_token`, `access_token`, 
               `access_token_expires_at`, `created_at`, `updated_at`, 
               `last_active_at`, `is_active`
        FROM `user_session`
        WHERE `open_id` = %s AND `is_active` = 1
        ORDER BY `last_active_at` DESC
        """
        
        try:
            result = execute_db_query(sql, (open_id,), fetch='all')
            sessions = []
            if result:
                for row in result:
                    sessions.append(UserSession(
                        session_id=row['session_id'],
                        open_id=row['open_id'],
                        refresh_token=row['refresh_token'],
                        access_token=row['access_token'],
                        access_token_expires_at=row['access_token_expires_at'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at'],
                        last_active_at=row['last_active_at'],
                        is_active=bool(row['is_active'])
                    ))
            return sessions
        except Exception as e:
            logger.exception(f"查找用户活跃sessions失败: {e}", exc_info=True)
            return []
    
    @staticmethod
    def update(session: UserSession) -> bool:
        """
        更新用户session
        
        Args:
            session: 用户session实例
            
        Returns:
            bool: 是否更新成功
        """
        sql = """
        UPDATE `user_session` 
        SET `refresh_token` = %s, `access_token` = %s, `access_token_expires_at` = %s,
            `updated_at` = %s, `last_active_at` = %s, `is_active` = %s
        WHERE `session_id` = %s
        """
        
        params = (
            session.refresh_token,
            session.access_token,
            session.access_token_expires_at,
            session.updated_at,
            session.last_active_at,
            session.is_active,
            session.session_id
        )
        
        try:
            execute_db_query(sql, params, commit=True)
            logger.info(f"用户session更新成功: session_id={session.session_id}")
            return True
        except Exception as e:
            logger.exception(f"更新用户session失败: {e}", exc_info=True)
            return False
    
    @staticmethod
    def deactivate_by_session_id(session_id: str) -> bool:
        """
        停用指定的session
        
        Args:
            session_id: session ID
            
        Returns:
            bool: 是否操作成功
        """
        sql = """
        UPDATE `user_session` 
        SET `is_active` = 0, `updated_at` = NOW()
        WHERE `session_id` = %s
        """
        
        try:
            execute_db_query(sql, (session_id,), commit=True)
            logger.info(f"session已停用: session_id={session_id}")
            return True
        except Exception as e:
            logger.exception(f"停用session失败: {e}", exc_info=True)
            return False
    
    @staticmethod
    def deactivate_all_by_open_id(open_id: str) -> bool:
        """
        停用用户的所有session
        
        Args:
            open_id: 用户open_id
            
        Returns:
            bool: 是否操作成功
        """
        sql = """
        UPDATE `user_session` 
        SET `is_active` = 0, `updated_at` = NOW()
        WHERE `open_id` = %s AND `is_active` = 1
        """
        
        try:
            execute_db_query(sql, (open_id,), commit=True)
            logger.info(f"用户所有session已停用: open_id={open_id}")
            return True
        except Exception as e:
            logger.exception(f"停用用户所有session失败: {e}", exc_info=True)
            return False
    
    @staticmethod
    def cleanup_expired_sessions(max_inactive_days: int = 30) -> int:
        """
        清理过期的session

        Args:
            max_inactive_days: 最大非活跃天数

        Returns:
            int: 清理的session数量
        """
        sql = """
        UPDATE `user_session`
        SET `is_active` = 0, `updated_at` = NOW()
        WHERE `is_active` = 1 AND `last_active_at` < DATE_SUB(NOW(), INTERVAL %s DAY)
        """

        try:
            result = execute_db_query(sql, (max_inactive_days,), fetch='count', commit=True)
            count = result if result else 0
            logger.info(f"清理了 {count} 个过期session")
            return count
        except Exception as e:
            logger.exception(f"清理过期session失败: {e}", exc_info=True)
            return 0

    @staticmethod
    def find_sessions_near_expiry(minutes_threshold: int = 10) -> List[UserSession]:
        """
        查找即将过期的access token对应的sessions

        Args:
            minutes_threshold: 过期阈值（分钟）

        Returns:
            List[UserSession]: 即将过期的session列表
        """
        sql = """
        SELECT `session_id`, `open_id`, `refresh_token`, `access_token`,
               `access_token_expires_at`, `created_at`, `updated_at`,
               `last_active_at`, `is_active`
        FROM `user_session`
        WHERE `is_active` = 1
          AND `access_token_expires_at` IS NOT NULL
          AND `access_token_expires_at` <= DATE_ADD(NOW(), INTERVAL %s MINUTE)
        ORDER BY `access_token_expires_at` ASC
        """

        try:
            result = execute_db_query(sql, (minutes_threshold,), fetch='all')
            sessions = []
            if result:
                for row in result:
                    sessions.append(UserSession(
                        session_id=row['session_id'],
                        open_id=row['open_id'],
                        refresh_token=row['refresh_token'],
                        access_token=row['access_token'],
                        access_token_expires_at=row['access_token_expires_at'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at'],
                        last_active_at=row['last_active_at'],
                        is_active=bool(row['is_active'])
                    ))
            return sessions
        except Exception as e:
            logger.exception(f"查找即将过期的sessions失败: {e}", exc_info=True)
            return []
