"""
Conversation sharing repository module.

This module provides data access functions for managing conversation sharing between users.
"""

import uuid
import time
from typing import Dict, Any, Optional
from mysql.connector import <PERSON>rror

from src.utils.logger import logger
from src.db.connection import execute_db_query

def create_share_link(username: str, email: str, conversation_id: str) -> Optional[str]:
    """
    Create a new share link for a conversation.

    Args:
        username (str): The username of the share owner
        email (str): The email of the share owner
        conversation_id (str): The ID of the conversation to share

    Returns:
        Optional[str]: The generated share ID if successful, None otherwise
    """
    if not username or not email or not conversation_id:
        logger.warning("Username, email, and conversation_id are required to create a share link")
        return None

    # Generate a unique share ID
    share_id = str(uuid.uuid4())
    
    # Current timestamp in milliseconds
    current_timestamp = int(time.time() * 1000)

    sql = """
        INSERT INTO share_map (share_id, conversation_id, owner_username, owner_email, created_at)
        VALUES (%s, %s, %s, %s, %s)
    """
    values = (share_id, conversation_id, username, email, current_timestamp)

    try:
        execute_db_query(sql, values, commit=True)
        logger.info(f"Created share link: {share_id} for conversation {conversation_id} owned by {username} ({email})")
        return share_id
    except Error as e:
        # Error already logged
        return None
    except Exception as e:
        logger.exception(f"Unexpected error creating share link: {e}", exc_info=True)
        return None

def get_shared_conversation(share_id: str) -> Optional[Dict[str, Any]]:
    """
    Get information about a shared conversation.

    Args:
        share_id (str): The ID of the share to retrieve

    Returns:
        Optional[Dict[str, Any]]: The share information if found, None otherwise
    """
    if not share_id:
        logger.warning("Share ID is required to get a shared conversation")
        return None

    sql = """
        SELECT share_id, conversation_id, owner_username, owner_email, created_at
        FROM share_map
        WHERE share_id = %s
    """
    values = (share_id,)

    try:
        result = execute_db_query(sql, values, fetch='one')
        if result:
            # Format timestamp
            ts_ms = result.get('created_at')
            if ts_ms and isinstance(ts_ms, int):
                try:
                    dt_object = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ts_ms / 1000))
                    result['formatted_time'] = dt_object
                except (TypeError, ValueError, OSError):
                    result['formatted_time'] = f"Invalid timestamp: {ts_ms}"
            else:
                result['formatted_time'] = "Unknown time"
                
            return result
        else:
            logger.warning(f"Shared conversation not found for share ID: {share_id}")
            return None
    except Error as e:
        # Error already logged
        return None
    except Exception as e:
        logger.exception(f"Unexpected error getting shared conversation: {e}", exc_info=True)
        return None
