"""
新一代Chat History Case标记系统 - 以chat_history_id为核心

这是一个完全重构的模块，解决了旧版本的核心问题：
1. 强制必须以chat_history_id为核心标识符
2. 消除NULL-chat_history_id的历史包袱
3. 建立清晰的API边界，无兼容补丁
"""

import json
from datetime import datetime
from typing import Optional, List, Dict, Any

from src.utils.logger import logger
from src.db.connection import execute_db_query
from src.db.database_enum import Database


class CaseRepository:
    """单一职责：管理chat_history级别的case标记"""
    
    VALID_CASE_TYPES = {'good', 'bad'}
    
    @classmethod
    def mark_chat_history_as_case(
        cls,
        chat_history_id: int,
        case_type: str,
        marked_by: str,
        feedback_tags: Optional[List[str]] = None,
        custom_feedback: Optional[str] = None
    ) -> bool:
        """
        将具体chat_history记录标记为case
        
        Args:
            chat_history_id: 必需的chat_history记录ID
            case_type: 必须是'good'或'bad'
            marked_by: 标记用户名
            feedback_tags: 反馈标签列表
            custom_feedback: 自定义反馈文本
        
        Returns:
            bool: 操作是否成功
        """
        # 严格的输入验证
        validation_result = cls._validate_case_params(
            chat_history_id=chat_history_id,
            case_type=case_type,
            marked_by=marked_by
        )
        
        if not validation_result['valid']:
            logger.exception(f"Case marking validation failed: {validation_result['error']}")
            return False
            
        return cls._insert_or_update_case_record(
            chat_history_id=chat_history_id,
            case_type=case_type,
            marked_by=marked_by,
            feedback_tags=feedback_tags,
            custom_feedback=custom_feedback
        )
    
    @classmethod
    def unmark_chat_history_as_case(
        cls,
        chat_history_id: int,
        case_type: str
    ) -> bool:
        """取消聊天历史消息的case标记"""
        if not cls._is_valid_case_type(case_type):
            return False
            
        try:
            table_name = f"{case_type}_case"
            delete_sql = f"DELETE FROM {table_name} WHERE chat_history_id = %s"
            
            affected_rows = execute_db_query(
                delete_sql, 
                (chat_history_id,), 
                fetch='count', 
                commit=True, 
                database=Database.CHATBI
            )
            
            success = affected_rows > 0
            logger.info(f"Unmark chat_history {chat_history_id} as {case_type}: {success}")
            return success
            
        except Exception as e:
            logger.exception(f"Error unmarking chat_history {chat_history_id}: {e}")
            return False
    
    @classmethod
    def get_case_status(cls, chat_history_id: int, case_type: str) -> Optional[Dict]:
        """获取特定chat_history记录的case状态"""
        if not cls._is_valid_case_type(case_type):
            return None
            
        try:
            table_name = f"{case_type}_case"
            sql = f"""
                SELECT * FROM {table_name} 
                WHERE chat_history_id = %s 
                LIMIT 1
            """
            result = execute_db_query(
                sql, 
                (chat_history_id,), 
                fetch='one', 
                database=Database.CHATBI
            )
            
            return result
            
        except Exception as e:
            logger.exception(f"Error getting case status for chat_history {chat_history_id}: {e}")
            return None
    
    @staticmethod
    def _validate_case_params(
        chat_history_id: int, 
        case_type: str, 
        marked_by: str
    ) -> Dict[str, Any]:
        """统一的参数验证逻辑"""
        if not isinstance(chat_history_id, int) or chat_history_id <= 0:
            return {'valid': False, 'error': 'chat_history_id必须是正整数'}
            
        if case_type not in CaseRepository.VALID_CASE_TYPES:
            return {'valid': False, 'error': f'case_type必须是{CaseRepository.VALID_CASE_TYPES}'}
            
        if not marked_by or not isinstance(marked_by, str):
            return {'valid': False, 'error': 'marked_by必须是有效的用户名'}
            
        # 验证chat_history记录存在性
        if not CaseRepository._does_chat_history_exist(chat_history_id):
            return {'valid': False, 'error': f'chat_history记录{chat_history_id}不存在'}
            
        return {'valid': True}
    
    @staticmethod
    def _is_valid_case_type(case_type: str) -> bool:
        """检查case_type有效性"""
        if case_type not in CaseRepository.VALID_CASE_TYPES:
            logger.exception(f"Invalid case_type: {case_type}. Must be one of {CaseRepository.VALID_CASE_TYPES}")
            return False
        return True
    
    @staticmethod
    def _does_chat_history_exist(chat_history_id: int) -> bool:
        """验证chat_history记录是否存在"""
        try:
            sql = "SELECT id FROM chat_history WHERE id = %s LIMIT 1"
            result = execute_db_query(sql, (chat_history_id,), fetch='one', database=Database.CHATBI)
            return result is not None
        except Exception as e:
            logger.exception(f"Error validating chat_history {chat_history_id}: {e}")
            return False
    
    @classmethod
    def _insert_or_update_case_record(
        cls,
        chat_history_id: int,
        case_type: str,
        marked_by: str,
        feedback_tags: Optional[List[str]] = None,
        custom_feedback: Optional[str] = None
    ) -> bool:
        """插入或更新case记录的底层实现"""
        
        # 获取与chat_history关联的conversation_id
        conversation_info = cls._get_conversation_id_by_chat_history(chat_history_id)
        if not conversation_info:
            logger.exception(f"Cannot find conversation_id for chat_history {chat_history_id}")
            return False
            
        conversation_id = conversation_info['conversation_id']
        
        # 处理反馈数据
        feedback_tags_json = None
        if feedback_tags and isinstance(feedback_tags, list):
            try:
                feedback_tags_json = json.dumps(feedback_tags, ensure_ascii=False)
            except Exception as e:
                logger.warning(f"Failed to serialize feedback tags: {e}")
        
        feedback_submitted_at = datetime.now()
        
        
        if case_type == 'bad':
            sql = """
                INSERT INTO bad_case (chat_history_id, conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    conversation_id = VALUES(conversation_id),
                    marked_by = VALUES(marked_by),
                    feedback_tags = VALUES(feedback_tags),
                    custom_feedback = VALUES(custom_feedback),
                    feedback_submitted_at = VALUES(feedback_submitted_at),
                    repair_status = 0,
                    updated_at = CURRENT_TIMESTAMP
            """
        else:  # good_case
            sql = """
                INSERT INTO good_case (chat_history_id, conversation_id, marked_by, feedback_tags, custom_feedback, feedback_submitted_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    conversation_id = VALUES(conversation_id),
                    marked_by = VALUES(marked_by),
                    feedback_tags = VALUES(feedback_tags),
                    custom_feedback = VALUES(custom_feedback),
                    feedback_submitted_at = VALUES(feedback_submitted_at),
                    updated_at = CURRENT_TIMESTAMP
            """
        
        values = (
            chat_history_id,
            conversation_id,
            marked_by,
            feedback_tags_json,
            custom_feedback,
            feedback_submitted_at
        )
        
        try:
            execute_db_query(sql, values, commit=True, database=Database.CHATBI)
            logger.info(f"Successfully marked chat_history {chat_history_id} as {case_type} case")
            return True
        except Exception as e:
            logger.exception(f"Database error marking chat_history {chat_history_id}: {e}")
            return False
    
    @staticmethod
    def _get_conversation_id_by_chat_history(chat_history_id: int) -> Optional[Dict[str, str]]:
        """通过chat_history_id获取conversation_id"""
        try:
            sql = "SELECT conversation_id FROM chat_history WHERE id = %s LIMIT 1"
            result = execute_db_query(sql, (chat_history_id,), fetch='one', database=Database.CHATBI)
            return result
        except Exception as e:
            logger.exception(f"Error getting conversation for chat_history {chat_history_id}: {e}")
            return None


# 废弃的兼容函数已移除，请直接使用CaseRepository类方法