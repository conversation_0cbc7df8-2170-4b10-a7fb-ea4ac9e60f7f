"""
飞书卡片操作模块
"""
import json
import uuid
import os
import re
from datetime import datetime
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.api.cardkit.v1 import *
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET
from .agent_message_formatter import THINKING_PROCESS_SPLITTER

# 卡片元素ID常量
THINKING_ELEMENT_ID = "markdown_thinking"
FINAL_ELEMENT_ID = "markdown_final_reply"
FOOTNOTE_ELEMENT_ID = "markdown_footnote"
FEEDBACK_ELEMENT_ID = "badcase_feedback"
GOOD_CASE_ELEMENT_ID = "goodcase_feedback"

# 创建lark客户端
lark_client = (
    lark.Client.builder()
    .app_id(APP_ID)
    .app_secret(APP_SECRET)
    .timeout(3)
    .log_level(lark.LogLevel.INFO)
    .build()
)

LOG_SERVER_NAME = bool(os.getenv("LOG_SERVER_NAME", False))
# 获取服务器名称
SERVER_NAME = os.popen("hostname").read().strip()

def get_update_footnote(chat_id: str, is_finished: bool = False) -> str:
    """生成更新脚注内容"""
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    link = f"[请点击此处在网页版查看结果]({HOST_NAME}/?chat={chat_id})"
    if is_finished:
        return f"> 完成于:{datetime.now().strftime('%m-%d %H:%M:%S')}\n> {link}"
    else:
        return f"> [生成中...] 最后更新于:{datetime.now().strftime('%m-%d %H:%M:%S')}\n> 如果长时间未更新，{link}"

def get_markdown_post_object(
    markdown_content: str,
    title: str = "来自ChatBI的回复",
    chat_id: str = None,
    user_id: str = None,
    final_response: str = None,
    chat_history_id: int = None,
) -> str:
    """获取Markdown消息对象
    https://open.feishu.cn/document/feishu-cards/card-json-v2-structure
    """
    is_finished = False if final_response is None else True
    # 生成脚注内容
    footnote = (
        get_update_footnote(chat_id, is_finished=is_finished)
        if chat_id
        else f"> [生成中...] 最后更新于:{datetime.now().strftime('%m-%d %H:%M:%S')}"
    )
    if LOG_SERVER_NAME:
        footnote += f"\n> [服务器: {SERVER_NAME}]"

    title_short=title
    if len(title_short)>30:
        title_short=title.replace('\n','')[:30]
        title_short=f"{title_short}..."

    content_json = {
        "schema": "2.0",
        "config": {
            "wide_screen_mode": True,
            "streaming_mode": not is_finished,
            "streaming_config": {
                "print_frequency_ms": {
                    "default": 50,
                },
                "print_step": {"default": 5},
                "print_strategy": "fast",
            },
            "width_mode": "fill",
            "enable_forward": True,
        },
        "body": {
            "elements": [
                {
                    "tag": "collapsible_panel",
                    "expanded": False,
                    "header": {
                        "title": {
                            "tag": "plain_text",
                            "content": "🤖思考过程(可点击展开)",
                        },
                        "vertical_align": "center",
                        "icon": {
                            "tag": "standard_icon",
                            "token": "down-small-ccm_outlined",
                            "color": "",
                            "size": "16px 16px",
                        },
                        "icon_position": "right",
                        "icon_expanded_angle": -180,
                    },
                    "border": {"color": "grey", "corner_radius": "5px"},
                    "vertical_spacing": "8px",
                    "padding": "8px 8px 8px 8px",
                    "elements": [
                        {
                            "tag": "markdown",
                            "content": markdown_content,
                            "element_id": THINKING_ELEMENT_ID,
                        }
                    ],
                },
                {
                    "tag": "markdown",
                    "content": THINKING_PROCESS_SPLITTER,
                },
                {
                    "tag": "markdown",
                    "content": "☕️ 请稍等..." if not is_finished else final_response,
                    "element_id": FINAL_ELEMENT_ID,
                },
                {
                    "tag": "markdown",
                    "content": footnote,
                    "element_id": FOOTNOTE_ELEMENT_ID,
                },
            ]
        },
        "header": {
            "template": "blue",
            "title": {"content": f"<at id=\"{user_id}\"></at> {title_short}", "tag": "lark_md"},
        },
    }
    return json.dumps(content_json, ensure_ascii=False)

def initial_card_message(
    message_id: str,
    card_content: str = "您的消息已收到，正在处理...",
    user_query: str = None,
    chat_id: str = None,
    user_id: str = None,
    final_response: str = None,
    chat_history_id: int = None,
) -> tuple[str, str]:
    """初始化卡片消息
    返回(card_id, element_id) 的组合"""

    # 构造创建Card请求对象
    request: CreateCardRequest = (
        CreateCardRequest.builder()
        .request_body(
            CreateCardRequestBody.builder()
            .type("card_json")
            .data(
                get_markdown_post_object(
                    card_content,
                    title=user_query,
                    chat_id=chat_id,
                    user_id=user_id,
                    final_response=final_response,
                    chat_history_id=chat_history_id,
                )
            )
            .build()
        )
        .build()
    )

    # 发起创建Card请求
    logger.info(
        f"Creating card for message {message_id} with content: {lark.JSON.marshal(request.body)}"
    )
    response: CreateCardResponse = lark_client.cardkit.v1.card.create(request)
    if not response.success():
        logger.exception(f"Failed to create card: {response.raw.content}")
        return None, None
    card_id = response.data.card_id
    logger.info(f"Card created successfully: {card_id}")

    # 构造消息请求对象
    request: ReplyMessageRequest = (
        ReplyMessageRequest.builder()
        .message_id(message_id)
        .request_body(
            ReplyMessageRequestBody.builder()
            .content(json.dumps({"type": "card", "data": {"card_id": card_id}}))
            .msg_type("interactive")
            .reply_in_thread(False)
            .uuid(str(uuid.uuid4()))
            .build()
        )
        .build()
    )

    # 发送消息的请求
    response: ReplyMessageResponse = lark_client.im.v1.message.reply(request)

    # 处理失败返回
    if not response.success():
        lark.logger.exception(f"send card failed, code resp: {response.raw.content}")
        return None, None  # Return None if failed

    # 处理业务结果
    logger.info(f"send card success, code resp: {lark.JSON.marshal(response.data)}")

    return (
        card_id,
        THINKING_ELEMENT_ID,
    )

def send_updates_to_card(
    card_id: str,
    markdown_content: str,
    element_id: str,  # 主内容（通常是思考过程）的元素ID
    sequence: int = 0,  # 初始序列号
) -> int:
    """更新卡片内容"""
    if not markdown_content:
        return sequence

    # 由于飞书卡片消息无法处理markdown图片，所以我们把以下这种markdown图片内容替换成“（请在网页版查看图片）”
    markdown_content = re.sub(
        r"!\[(.*?)\]\((https?://[^)]+)\.(png|jpg|jpeg)\)",
        r"（请在网页版查看图片）",
        markdown_content,
    )

    # 构造最终答案更新请求
    final_answer_request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(element_id)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(markdown_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        final_answer_request
    )
    if not response.success():
        logger.warning(
            f"Update card element failed (seq: {sequence}), card_id:{card_id}:{element_id}. Response: {response.raw.content}"
        )

    return sequence + 1  # 返回最终的序列号

def after_button_feedback(card_id: str, button_element_id: str, feedback_text: str) -> int:
    """按钮反馈后的卡片更新

    Args:
        card_id: 卡片ID
        button_element_id: 按钮元素ID
        feedback_text: 反馈文本

    Returns:
        响应状态码
    """
    if not card_id:
        logger.exception("card_id is empty")
        return 0

    post_json = {
        "disabled": True,
        "text": {"content": feedback_text},
    }
    # 使用大的序列号，确保覆盖之前的所有操作
    year_2025 = datetime(2025, 1, 1).timestamp()
    sequence = int(datetime.now().timestamp() - year_2025)
    logger.info(f"after_button_feedback, card_id:{card_id}, button_element_id:{button_element_id}, feedback_text:{feedback_text}, sequence:{sequence}")

    request: PatchCardElementRequest = (
        PatchCardElementRequest.builder()
        .card_id(card_id)
        .element_id(button_element_id)
        .request_body(
            PatchCardElementRequestBody.builder()
            .partial_element(json.dumps(post_json, ensure_ascii=False))
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )

    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.patch(
        request
    )
    return response.code

def after_badcase_mark(card_id: str, **kwargs) -> int:
    """标记为Bad Case后的卡片更新"""
    return after_button_feedback(
        card_id=card_id,
        button_element_id=FEEDBACK_ELEMENT_ID,
        feedback_text="您已标记为数据不准案例，感谢您的反馈！",
    )

def after_goodcase_mark(card_id: str, **kwargs) -> int:
    """标记为Good Case后的卡片更新"""
    return after_button_feedback(
        card_id=card_id,
        button_element_id=GOOD_CASE_ELEMENT_ID,
        feedback_text="您已标记为Good Case，感谢您的反馈！",
    )
    
def after_sku_choice_selection(card_id: str, choice: str = None, index: int = 0, **kwargs) -> int:
    """用户选择SKU后的卡片更新"""
    return after_button_feedback(
        card_id=card_id,
        button_element_id=f"ai_choice_{index}",
        feedback_text=f"您选择: {choice}",
    )

def append_feedback_buttons_to_card(card_id: str, chat_id: str, sequence: int, chat_history_id: int = None) -> int:
    """将好评和差评按钮插入到指定卡片中
    
    在卡片的最终回复元素之后插入好评和差评按钮，用于收集用户反馈
    
    Args:
        card_id: 飞书卡片ID
        chat_id: 会话ID
        sequence: 序列号
        chat_history_id: 聊天历史ID
        
    Returns:
        更新后的序列号
    """
    if not card_id:
        logger.exception("card_id is empty")
        return sequence
        
    # 构造按钮组件元素
    feedback_buttons = {
        "tag": "column_set",
        "flex_mode": "stretch",
        "background_style": "default",
        "columns": [
            {
                "tag": "column",
                "width": "auto",
                "elements": [
                    {
                        "tag": "button",
                        "element_id": GOOD_CASE_ELEMENT_ID,
                        "type": "primary",
                        "size": "small",
                        "text": {"tag": "plain_text", "content": "给你点赞👍"},
                        "disabled": False,
                        "behaviors": [
                            {"type": "callback", "value": {
                                "conversation_id": f"{chat_id}",
                                "chat_history_id": chat_history_id,
                                "card_id": card_id,
                                "action": "good_case"
                            }}
                        ]
                    }
                ]
            },
            {
                "tag": "column",
                "width": "auto",
                "elements": [
                    {
                        "tag": "button",
                        "element_id": FEEDBACK_ELEMENT_ID,
                        "type": "primary",
                        "size": "small",
                        "text": {"tag": "plain_text", "content": "数据不准👎"},
                        "disabled": False,
                        "behaviors": [
                            {"type": "callback", "value": {
                                "conversation_id": f"{chat_id}",
                                "chat_history_id": chat_history_id,
                                "card_id": card_id,
                                "action": "bad_case"
                            }}
                        ]
                    }
                ]
            }
        ]
    }
    
    # 构造创建卡片元素请求
    request: CreateCardElementRequest = (
        CreateCardElementRequest.builder()
        .card_id(card_id)
        .request_body(
            CreateCardElementRequestBody.builder()
            .type("insert_after")
            .target_element_id(FINAL_ELEMENT_ID)
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .elements(json.dumps([feedback_buttons], ensure_ascii=False))
            .build()
        )
        .build()
    )
    
    # 发起请求
    response: CreateCardElementResponse = lark_client.cardkit.v1.card_element.create(request)
    
    # 处理失败返回
    if not response.success():
        logger.exception(
            f"Append feedback buttons failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
        )
        return sequence
    
    # 处理成功返回
    logger.info(f"Append feedback buttons success, response: {lark.JSON.marshal(response.raw)}")
    return sequence + 1

def append_ai_question_buttons_to_card(
    card_id: str, 
    conversation_id: str, 
    sequence: int, 
    question: str, 
    choices: list[str], 
    chat_history_id: int = None
) -> int:
    """向卡片添加AI问题和选择按钮
    
    用于AI向用户提出确认问题并提供选项按钮，例如：
    "我找到了多个SKU，请问您是需要哪一个？"
    
    Args:
        card_id: 飞书卡片ID
        conversation_id: 会话ID
        sequence: 序列号
        question: AI提出的问题文本
        choices: 用户可选择的选项列表
        chat_history_id: 聊天历史ID
        
    Returns:
        更新后的序列号
    """
    if not card_id:
        logger.exception("card_id is empty")
        return sequence
        
    if not question or not choices:
        logger.exception("question or choices is empty")
        return sequence
        
    # 构造问题和选择按钮元素
    elements_to_add = []
    
    # 添加问题的 Markdown 元素
    question_element = {
        "tag": "markdown",
        "content": f"---\n\n**🤖 {question}**",
        "element_id": f"ai_question_{sequence}"
    }
    elements_to_add.append(question_element)
    
    # 为每个选择创建按钮，使用列式布局（每行一个按钮）
    for i,choice in enumerate(choices, start=1):
        button_element = {
            "tag": "button",
            "element_id": f"ai_choice_{i}",
            "type": "primary",
            "size": "small",
            "width": "default",
            "text": {"tag": "plain_text", "content": f"{i}. {choice}"},
            "disabled": False,
            "behaviors": [
                {"type": "callback", "value": {
                    "conversation_id": f"{conversation_id}",
                    "chat_history_id": chat_history_id,
                    "card_id": card_id,
                    "action": "choices_feedback",
                    "choice": choice,
                    "index": i
                }}
            ]
        }
        elements_to_add.append(button_element)

    note_element = {
        "tag": "markdown",
        "content": f"---\n\n**请点击按钮选择您要的SKU，或者长按此消息，回复数字选择多个SKU。**",
    }
    elements_to_add.append(note_element)
    
    # 构造创建卡片元素请求
    request: CreateCardElementRequest = (
        CreateCardElementRequest.builder()
        .card_id(card_id)
        .request_body(
            CreateCardElementRequestBody.builder()
            .type("insert_after")
            .target_element_id(FINAL_ELEMENT_ID)
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .elements(json.dumps(elements_to_add, ensure_ascii=False))
            .build()
        )
        .build()
    )
    
    # 发起请求
    response: CreateCardElementResponse = lark_client.cardkit.v1.card_element.create(request)
    
    # 处理失败返回
    if not response.success():
        logger.exception(
            f"Append AI question buttons failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
        )
        return sequence
    
    # 处理成功返回
    logger.info(f"Append AI question buttons success, question: '{question}', choices: {choices}, response: {lark.JSON.marshal(response.raw)}")
    return sequence + 1

def finish_sku_choice_card(card_id: str, sequence: int) -> int:
    """完成SKU选择卡片，不添加反馈按钮

    Args:
        card_id: 卡片ID
        sequence: 序列号

    Returns:
        int: 更新后的序列号
    """
    if not card_id:
        logger.exception("card_id is empty")
        return sequence

    # 只更新脚注到最终状态，不添加反馈按钮
    final_footnote_content = f"> 任务完成于:{datetime.now().strftime('%m-%d %H:%M:%S')}"

    # 使用同步方式更新脚注
    request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FOOTNOTE_ELEMENT_ID)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(final_footnote_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        request
    )

    sequence += 1

    # 设置卡片配置（不添加反馈按钮）
    json_str = json.dumps(
        {
            "config": {
                "enable_forward": True,
                "enable_forward_interaction": False,
                "streaming_mode": False,
                "update_multi": True,
                "width_mode": "fill",
            }
        }
    )

    # 构造请求对象
    request: SettingsCardRequest = (
        SettingsCardRequest.builder()
        .card_id(card_id)
        .request_body(
            SettingsCardRequestBody.builder()
            .settings(json_str)
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )

    # 发起请求
    response: SettingsCardResponse = lark_client.cardkit.v1.card.settings(request)

    logger.info(f"SKU选择卡片完成，卡片ID: {card_id}")
    return sequence + 1

def send_finished_message_to_card(card_id: str, chat_id: str, sequence: int, chat_history_id: int = None) -> int:
    """更新脚注到最终状态，添加反馈按钮，然后发送最终设置更新"""
    final_footnote_content = f"> 任务完成于:{datetime.now().strftime('%m-%d %H:%M:%S')}"

    # 使用同步方式更新脚注
    request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FOOTNOTE_ELEMENT_ID)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(final_footnote_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        request
    )

    sequence += 1

    # 动态添加反馈按钮
    sequence = append_feedback_buttons_to_card(
        card_id=card_id,
        chat_id=chat_id, 
        sequence=sequence,
        chat_history_id=chat_history_id
    )

    json_str = json.dumps(
        {
            "config": {
                "enable_forward": True,
                "enable_forward_interaction": False,
                "streaming_mode": False,
                "update_multi": True,
                "width_mode": "fill",
            }
        }
    )
    # 构造请求对象
    request: SettingsCardRequest = (
        SettingsCardRequest.builder()
        .card_id(card_id)
        .request_body(
            SettingsCardRequestBody.builder()
            .settings(json_str)
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )

    # 发起请求
    response: SettingsCardResponse = lark_client.cardkit.v1.card.settings(request)
    return sequence + 1
