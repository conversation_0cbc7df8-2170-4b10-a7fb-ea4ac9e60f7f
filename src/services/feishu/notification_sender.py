"""
飞书通知发送模块
"""

import json
import os
from datetime import datetime
from src.utils.logger import logger
from .message_core import send_message_to_chat, reply_interactive_message

# Bad Case通知群聊ID（支持多个，以逗号分隔）
BAD_CASE_NOTIFICATION_CHAT_ID = os.getenv("BAD_CASE_NOTIFICATION_CHAT_ID")
# Good Case通知群聊ID（支持多个，以逗号分隔）, 如果没有配置则使用BAD_CASE_NOTIFICATION_CHAT_ID
GOOD_CASE_NOTIFICATION_CHAT_ID = os.getenv(
    "GOOD_CASE_NOTIFICATION_CHAT_ID", BAD_CASE_NOTIFICATION_CHAT_ID
)


def _parse_chat_ids(chat_ids_str: str) -> list[str]:
    """解析逗号分隔的群聊ID字符串，返回群聊ID列表"""
    if not chat_ids_str:
        return []
    return [chat_id.strip() for chat_id in chat_ids_str.split(",") if chat_id.strip()]


def _get_notify_chat_ids(case_type: str) -> list[str]:
    """根据case类型获取通知群聊ID列表"""
    if case_type == "good":
        chat_ids_str = GOOD_CASE_NOTIFICATION_CHAT_ID
    else:
        chat_ids_str = BAD_CASE_NOTIFICATION_CHAT_ID

    return _parse_chat_ids(chat_ids_str)


def _truncate_message(message: str, max_length: int = 200) -> str:
    """截断消息内容"""
    if not message:
        return ""
    return message[:max_length] + "..." if len(message) > max_length else message


def _build_dashboard_link(conversation_id: str) -> str:
    """构建Dashboard链接"""
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    return f"{HOST_NAME}/dashboard?chat={conversation_id}"


def _build_user_message_section(user_message: str, case_type: str, action: str) -> str:
    """构建用户消息内容部分"""
    if not user_message:
        return ""

    truncated_message = _truncate_message(user_message)

    # Good Case取消标记时使用引用格式
    if case_type == "good" and action == "unmark":
        return f"\n**用户消息内容**:\n> {truncated_message}\n"
    else:
        return f"\n**用户消息内容**:\n{truncated_message}\n"


def _build_assistant_message_section(assistant_message: str) -> str:
    """构建assistant回复内容部分"""
    if not assistant_message:
        return ""

    truncated_message = _truncate_message(assistant_message, max_length=500)
    return f"\n**ChatBI的回复**:\n{truncated_message}\n"


def send_case_notification(
    *,
    conversation_id: str,
    user_name: str = None,
    user_message: str = None,
    case_type: str = "bad",  # "bad" or "good"
    action: str = "mark",  # "mark" or "unmark"
    conversation_user_name: str = None,  # 对话所属用户名
    last_assistant_message: str = None,  # 最后一条assistant回复内容
    chat_history_id: int = None,  # 具体的chat_history_id
) -> bool:
    """
    通用的Case标记/取消标记通知发送函数

    Args:
        conversation_id (str): 对话ID
        user_name (str, optional): 标记用户名称
        user_message (str, optional): 用户最后一条消息内容
        case_type (str): "bad" 或 "good"
        action (str): "mark" 或 "unmark"
        conversation_user_name (str, optional): 对话所属用户名称
        last_assistant_message (str, optional): 最后一条assistant回复内容

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    # 通知群聊ID列表
    notify_chat_ids = _get_notify_chat_ids(case_type)
    if not notify_chat_ids:
        logger.warning(f"未配置{case_type.capitalize()} Case通知群聊ID，跳过发送通知")
        return False

    # 配置模板
    config_map = {
        ("bad", "mark"): {
            "color": "red",
            "icon": "🚨",
            "title": "Bad Case 标记通知",
            "extra": "请相关人员及时关注和处理。",
            "action_label": "标记时间",
            "footer": "",
        },
        ("bad", "unmark"): {
            "color": "green",
            "icon": "✅",
            "title": "Bad Case 取消标记通知",
            "extra": "",
            "action_label": "取消标记时间",
            "footer": "该对话已被取消Bad Case标记。",
        },
        ("good", "mark"): {
            "color": "green",
            "icon": "🌟",
            "title": "Good Case 标记通知",
            "extra": "",
            "action_label": "标记时间",
            "footer": "该对话已被标记为Good Case，可作为参考示例。",
        },
        ("good", "unmark"): {
            "color": "yellow",
            "icon": "⚠️",
            "title": "Good Case 取消标记通知",
            "extra": "",
            "action_label": "取消标记时间",
            "footer": "该对话已被取消Good Case标记。",
        },
    }
    conf = config_map.get((case_type, action))
    if not conf:
        logger.exception(f"未知case_type/action: {case_type}/{action}")
        return False

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 构建用户信息部分
    marker_user_info = (
        f"**标记用户**: {user_name}" if user_name else "**标记用户**: 未知用户"
    )
    conversation_user_info = (
        f"**对话用户**: {conversation_user_name}"
        if conversation_user_name
        else "**对话用户**: 未知用户"
    )

    dashboard_link = _build_dashboard_link(conversation_id)
    user_message_section = _build_user_message_section(user_message, case_type, action)
    assistant_message_section = _build_assistant_message_section(last_assistant_message)

    # 构造Markdown内容
    chat_history_info = f"\n**消息ID**: `{chat_history_id}`" if chat_history_id else ""
    markdown_content = f"""{marker_user_info}
{conversation_user_info}
**对话ID**: `{conversation_id}`{chat_history_info}
**{conf['action_label']}**: {timestamp}{user_message_section}{assistant_message_section}

**操作**: [📊 查看对话详情]({dashboard_link})

---
{conf['footer'] or conf['extra']}"""

    # 构造飞书交互式卡片消息
    card_content = {
        "header": {
            "template": conf["color"],
            "title": {
                "content": f"{conf['icon']} {conf['title']}",
                "tag": "lark_md",
            },
        },
        "elements": [
            {
                "tag": "markdown",
                "content": markdown_content,
            }
        ],
    }

    # 向所有配置的群聊发送通知
    success = True
    for chat_id in notify_chat_ids:
        if not send_message_to_chat(
            chat_id, json.dumps(card_content), msg_type="interactive"
        ):
            success = False
            logger.exception(f"发送通知到群聊 {chat_id} 失败")

    return success


# 便捷函数
def send_bad_case_notification(
    conversation_id: str,
    user_name: str = None,
    user_message: str = None,
    conversation_user_name: str = None,
    last_assistant_message: str = None,
    chat_history_id: int = None,
) -> bool:
    """发送Bad Case标记通知到指定群聊"""
    return send_case_notification(
        conversation_id=conversation_id,
        user_name=user_name,
        user_message=user_message,
        case_type="bad",
        action="mark",
        conversation_user_name=conversation_user_name,
        last_assistant_message=last_assistant_message,
        chat_history_id=chat_history_id,
    )


def send_bad_case_unmark_notification(
    conversation_id: str,
    user_name: str = None,
    user_message: str = None,
    conversation_user_name: str = None,
    last_assistant_message: str = None,
    chat_history_id: int = None,
) -> bool:
    """发送Bad Case取消标记通知到指定群聊"""
    return send_case_notification(
        conversation_id=conversation_id,
        user_name=user_name,
        user_message=user_message,
        case_type="bad",
        action="unmark",
        conversation_user_name=conversation_user_name,
        last_assistant_message=last_assistant_message,
        chat_history_id=chat_history_id,
    )


def send_good_case_notification(
    conversation_id: str,
    user_name: str = None,
    user_message: str = None,
    conversation_user_name: str = None,
    last_assistant_message: str = None,
    chat_history_id: int = None,
) -> bool:
    """发送Good Case标记通知到指定群聊"""
    return send_case_notification(
        conversation_id=conversation_id,
        user_name=user_name,
        user_message=user_message,
        case_type="good",
        action="mark",
        conversation_user_name=conversation_user_name,
        last_assistant_message=last_assistant_message,
        chat_history_id=chat_history_id,
    )


def send_good_case_unmark_notification(
    conversation_id: str,
    user_name: str = None,
    user_message: str = None,
    conversation_user_name: str = None,
    last_assistant_message: str = None,
    chat_history_id: int = None,
) -> bool:
    """发送Good Case取消标记通知到指定群聊"""
    return send_case_notification(
        conversation_id=conversation_id,
        user_name=user_name,
        user_message=user_message,
        case_type="good",
        action="unmark",
        conversation_user_name=conversation_user_name,
        last_assistant_message=last_assistant_message,
        chat_history_id=chat_history_id,
    )


async def send_recommendation_card(
    chat_id: str, user_name: str, recommendations: list[str], message_id: str = None
) -> bool:
    """
    发送推荐问题卡片到指定聊天

    Args:
        chat_id (str): 聊天ID
        user_name (str): 用户姓名
        recommendations (list[str]): 推荐问题列表
        message_id (str, optional): 要回复的消息ID，如果提供则回复该消息，否则发送新消息

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not chat_id or not recommendations:
        logger.warning("聊天ID或推荐内容为空，无法发送推荐卡片")
        return False

    try:
        # 构建推荐问题的Markdown内容
        recommendation_text = (
            "基于您的历史查询和其他用户的使用情况，我为您推荐以下问题：\n\n"
        )
        for i, question in enumerate(recommendations, 1):
            recommendation_text += f"{i}. {question}\n"

        # 构造推荐卡片内容，使用正确的飞书卡片格式
        feishu_message_obj = {
            "schema": "2.0",
            "header": {
                "template": "blue",
                "title": {
                    "content": f"**👏 {user_name}，欢迎回来，为您推荐以下问题**",
                    "tag": "lark_md",
                },
            },
            "body": {
                "elements": [
                    {
                        "tag": "markdown",
                        "content": recommendation_text,
                    },
                    {
                        "tag": "markdown",
                        "content": "[👉新手必读：当机器人反问了我一个问题时该怎么办？](https://summerfarm.feishu.cn/wiki/KGCuwV2JeiGEsBku0kicEaEznuf)",
                    },
                    {
                        "tag": "markdown",
                        "content": f"> 推荐时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n> ",
                    },
                ]
            },
        }

        logger.info(
            f"准备发送推荐卡片到聊天 {chat_id}，包含 {len(recommendations)} 条推荐"
        )

        # 根据是否提供message_id决定使用回复功能还是普通发送功能
        if message_id:
            logger.info(f"回复消息 {message_id} 发送推荐卡片")
            return reply_interactive_message(message_id, json.dumps(feishu_message_obj))
        else:
            logger.info(f"发送新推荐卡片到聊天 {chat_id}")
            return send_message_to_chat(
                chat_id, json.dumps(feishu_message_obj), msg_type="interactive"
            )

    except Exception as e:
        logger.exception(f"发送推荐卡片时发生异常: {str(e)}", exc_info=True)
        return False
