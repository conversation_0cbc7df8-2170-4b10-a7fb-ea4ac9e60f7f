"""
飞书文档搜索相关工具。
"""

import os
import uuid
import aiohttp
import asyncio
import random
from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager
from src.services.agent.tools.feishu_group_chat_content_tool import search_feishu_group_chat_content

FILE_CONTENT_LIMIT=int(os.getenv("FILE_CONTENT_LIMIT", 2000))

# 并发控制：最多3个并发请求
CONCURRENT_LIMIT = 3

# 飞书文档类型表：https://open.feishu.cn/document/server-docs/docs/wiki-v2/search_wiki
FEISHU_DOC_TYPE_SET=set([1,5,6,7,8]) # 取doc/docx/wiki/slide/file

def _generate_search_keywords(query: str) -> List[str]:
    """根据空格分词，生成多个关键词组合用于并行搜索。
    
    Args:
        query: 原始查询字符串
        
    Returns:
        List[str]: 生成的关键词列表，按搜索priority排序
        
    示例:
        "收入证明 开具流程 申请" -> ["收入证明", "收入证明 开具流程", "收入证明 开具流程 申请"]
        "如何使用系统" -> ["如何使用系统"]  # 无空格，直接返回
    """
    if not query.strip():
        return [query]
    
    # 如果查询不包含空格，直接返回原查询
    if ' ' not in query.strip():
        return [query.strip()]
    
    # 按空格分割，保留空格和原始格式
    terms = query.split()
    terms = [term.strip() for term in terms if term.strip()]
    
    # 如果只有一个词，返回原查询
    if len(terms) <= 1:
        return [query.strip()]
    
    # 生成逐步增加的关键词组合，保留空格
    keywords = []
    for i in range(1, len(terms) + 1):
        keyword = ' '.join(terms[:i])
        if keyword and keyword not in keywords:
            keywords.append(keyword)
    
    # 限制最多生成5个关键词，避免过多搜索
    return keywords[:5]


async def search_feishu_docs(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 10,
    get_content: bool = True
) -> Tuple[List[Dict[str, Any]], str]:
    """搜索飞书文档并获取内容，同时并行搜索群聊消息。

    该工具用于在飞书知识库中搜索相关文档，可以帮助AI获取公司内部的业务知识和规则定义。
    现在会同时搜索飞书文档和群聊消息，为AI提供更丰富的知识来源。

    新增功能：对于包含空格的查询，会自动拆分为多个关键词组合进行并行搜索，提高召回率。
    工作流程：
    1. 智能拆解含空格的查询为多个关键词组合（保留空格）
    2. 并行执行各个关键词的文档搜索和群聊搜索
    3. 去重合并所有搜索结果
    4. 如果get_content为True，会进一步调用详情接口获取每个文档的具体内容
    5. 合并所有搜索结果，并标识数据来源

    Args:
        wrapper: 包含用户信息的上下文包装器。
        query: 搜索关键词，建议使用具体的业务术语或问题描述。支持空格分词优化。
        page_size: 返回结果数量，默认为10，最大不超过50。
        get_content: 是否获取文档详细内容，默认为True。如果为True，会进一步调用详情接口获取每个文档的具体内容。您可以根据需要来决定是否调用此工具。

    Returns:
        Tuple[List[Dict[str, Any]], str]: 搜索结果列表和描述信息。
        搜索结果包含：title（标题）、url（链接）、obj_token（文档标识）、content（内容，仅当get_content=True时）、source（数据来源：'document'或群聊名称）
    """
    user_info = wrapper.context
    access_token = user_info.access_token

    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"

    search_id = str(uuid.uuid4())
    logger.info(f"用户开始搜索飞书文档和群聊，搜索ID: {search_id}, 关键词: {query}, access_token: {access_token[:20]}...")

    # 生成多个关键词进行并行搜索
    search_keywords = _generate_search_keywords(query)
    logger.info(f"原始查询: {query}, 拆分为 {len(search_keywords)} 个关键词: {search_keywords}")

    # 并行调用所有关键词的文档搜索和群聊搜索
    try:
        # 创建所有并行任务
        all_tasks = []
        
        # 为每个关键词创建文档搜索任务
        for keyword in search_keywords:
            doc_task = _search_documents_only(wrapper, keyword, page_size, get_content)
            all_tasks.append(doc_task)
        
        # 为每个关键词创建群聊搜索任务
        for keyword in search_keywords:
            chat_task = _search_group_chats_safe(wrapper, keyword)
            all_tasks.append(chat_task)

        # 并行执行所有搜索任务
        all_results = await asyncio.gather(*all_tasks, return_exceptions=True)

        # 分离文档搜索和群聊搜索结果
        num_keywords = len(search_keywords)
        doc_results_list = all_results[:num_keywords]
        chat_results_list = all_results[num_keywords:]

        # 合并所有文档搜索结果
        doc_items = []
        for result in doc_results_list:
            if isinstance(result, Exception):
                logger.exception(f"某个文档搜索任务失败: {result}")
                continue
            else:
                items, _ = result
                # 为文档结果添加来源标识
                for item in items:
                    item['source'] = 'document'
                doc_items.extend(items)

        # 合并所有群聊搜索结果
        chat_items = []
        for result in chat_results_list:
            if isinstance(result, Exception):
                logger.exception(f"某个群聊搜索任务失败: {result}")
                continue
            else:
                items, _ = result
                # 为群聊结果添加来源标识和统一格式
                for item in items:
                    # 使用群聊名称作为数据来源标识
                    chat_name = item.get('chat_name', '未知群聊')
                    item['source'] = chat_name
                    # 统一字段格式，将群聊消息格式转换为类似文档的格式
                    if 'content' in item and 'title' not in item:
                        # 从消息内容中提取前50个字符作为标题，并包含群聊名称
                        content_preview = item['content'][:50] + "..." if len(item.get('content', '')) > 50 else item.get('content', '')
                        item['title'] = f"[{chat_name}] {content_preview}"
                    if 'message_id' in item and 'obj_token' not in item:
                        item['obj_token'] = item['message_id']  # 使用message_id作为标识符
                    if 'url' not in item:
                        item['url'] = f"飞书群聊消息 (群聊: {chat_name})"
                chat_items.extend(items)

        # 去重合并：使用obj_token作为唯一标识符去重
        seen_tokens = set()
        unique_doc_items = []
        for item in doc_items:
            token = item.get('obj_token')
            if token and token not in seen_tokens:
                seen_tokens.add(token)
                unique_doc_items.append(item)

        unique_chat_items = []
        for item in chat_items:
            token = item.get('obj_token')
            if token and token not in seen_tokens:
                seen_tokens.add(token)
                unique_chat_items.append(item)

        # 合并最终结果，限制总数量
        all_items = unique_doc_items + unique_chat_items
        # 按相关性排序（飞书搜索已排序）并限制总数
        all_items = all_items[:page_size * 2]  # 允许稍微多一些，因为合并了多个搜索

        # 生成综合描述信息
        total_docs = len(unique_doc_items)
        total_chats = len(unique_chat_items)
        total_items = len(all_items)

        if total_items == 0:
            return [], f"使用 {len(search_keywords)} 个关键词组合['{'|'.join(search_keywords)}']，未找到与'{query}' 相关的文档或群聊消息"

        description_parts = []
        if total_docs > 0:
            description_parts.append(f"{total_docs} 个文档")
        if total_chats > 0:
            description_parts.append(f"{total_chats} 条群聊消息")

        keyword_desc = f"使用关键词组合{'、'.join([f'『{k}』' for k in search_keywords])}"
        description = f"{keyword_desc}，成功搜索到 {' 和 '.join(description_parts)}，共 {total_items} 条结果"
        if get_content:
            description += "并获取了内容"

        logger.info(f"多关键词并行搜索完成: {description}")
        return all_items, description

    except Exception as e:
        error_msg = f"搜索过程中发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索失败: {error_msg}"


async def _search_documents_only(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 10,
    get_content: bool = True
) -> Tuple[List[Dict[str, Any]], str]:
    """仅搜索飞书文档的内部函数"""
    user_info = wrapper.context
    access_token = user_info.access_token

    try:
        # 调用搜索接口
        search_url = f"https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size={page_size}"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }
        search_data = {'query': query}

        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(search_url, headers=headers, json=search_data) as response:
                if response.status != 200:
                    error_msg = f"搜索接口调用失败，状态码: {response.status}, 响应内容: {await response.text()}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"

                search_result = await response.json()

                if search_result.get('code') != 0:
                    error_msg = f"搜索接口返回错误: {search_result.get('msg', '未知错误')}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书文档失败: {error_msg}"

                items = search_result.get('data', {}).get('items', [])
                filtered_items = []
                for item in items:
                    logger.info(f"搜索到文档: {item['title']}, 链接: {item['url']}, obj_type: {item['obj_type']}")
                    if item['obj_type'] in FEISHU_DOC_TYPE_SET:
                        filtered_items.append(item)
                    else:
                        logger.info(f"跳过文档: {item['title']}, 链接: {item['url']}, obj_type: {item['obj_type']}")
                items = filtered_items

                if not items:
                    return [], f"未找到与关键词 '{query}' 相关的文档"

                # 如果不需要获取内容，直接返回搜索结果
                if not get_content:
                    return items, f"成功搜索到 {len(items)} 个相关文档"

                # 并发获取每个文档的详细内容
                enriched_items = await _get_documents_content_concurrently(access_token, items, get_all=False)

                return enriched_items, f"成功搜索到 {len(enriched_items)} 个相关文档并获取了内容"

    except Exception as e:
        error_msg = f"搜索飞书文档时发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索飞书文档失败: {error_msg}"


async def _search_group_chats_safe(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 30,
    get_content: bool = True
) -> Tuple[List[Dict[str, Any]], str]:
    """安全地搜索群聊消息的包装函数，确保错误不会影响文档搜索"""
    try:
        logger.info(f"开始并行搜索群聊消息，关键词: {query}")
        return await search_feishu_group_chat_content(wrapper, query, page_size, get_content)
    except Exception as e:
        logger.warning(f"群聊搜索出现异常，但不影响文档搜索: {str(e)}")
        return [], f"群聊搜索失败: {str(e)}"


async def get_feishu_doc_content_tool(
    wrapper: RunContextWrapper[UserInfo], 
    obj_token: str
) -> Tuple[Optional[str], str]:
    """获取指定飞书文档的纯文本内容。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        obj_token: 文档的obj_token标识符。

    Returns:
        Tuple[Optional[str], str]: 文档内容和描述信息。
    """
    user_info = wrapper.context
    access_token = user_info.access_token
    
    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return None, f"获取文档内容失败: {error_msg}"
    
    logger.info(f"开始获取飞书文档内容，obj_token: {obj_token}")
    
    content = await _get_feishu_doc_content(access_token, obj_token, get_all=True)
    
    if content:
        return content, f"成功获取文档内容，长度: {len(content)} 字符"
    else:
        return None, f"获取文档内容失败，obj_token: {obj_token}"


async def _get_documents_content_concurrently(
    access_token: str,
    items: List[Dict[str, Any]],
    get_all: bool = False
) -> List[Dict[str, Any]]:
    """并发获取多个文档的内容。

    Args:
        access_token: 飞书访问令牌。
        items: 文档列表，每个文档包含obj_token。
        get_all: 是否获取全部内容，默认为False。

    Returns:
        List[Dict[str, Any]]: 包含内容的文档列表。
    """
    # 创建信号量来控制并发数量
    semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

    async def get_single_doc_content(item: Dict[str, Any]) -> Dict[str, Any]:
        """获取单个文档内容的包装函数"""
        async with semaphore:
            obj_token = item.get('obj_token')
            if obj_token:
                content = await _get_feishu_doc_content(access_token, obj_token, get_all)
                item['content'] = content
            else:
                item['content'] = None
            return item

    # 创建并发任务
    tasks = [get_single_doc_content(item.copy()) for item in items]

    # 并发执行所有任务
    logger.info(f"开始并发获取 {len(items)} 个文档的内容，并发数: {CONCURRENT_LIMIT}")
    enriched_items = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理异常结果
    result_items = []
    for i, result in enumerate(enriched_items):
        if isinstance(result, Exception):
            logger.exception(f"获取第 {i+1} 个文档内容时发生异常: {result}")
            # 保留原始项目，但标记内容获取失败
            original_item = items[i].copy()
            original_item['content'] = f"获取内容失败: {str(result)}"
            result_items.append(original_item)
        else:
            result_items.append(result)

    logger.info(f"并发获取文档内容完成，成功: {len([r for r in result_items if r.get('content') and not r['content'].startswith('获取内容失败')])}/{len(items)}")
    return result_items


async def _get_feishu_doc_content(access_token: str, obj_token: str, get_all: bool = False) -> Optional[str]:
    """内部函数：获取飞书文档的纯文本内容。

    Args:
        access_token: 飞书访问令牌。
        obj_token: 文档的obj_token标识符。
        get_all: 是否获取全部内容，默认为False。

    Returns:
        Optional[str]: 文档的纯文本内容，获取失败时返回None。
    """
    max_retries = 3
    retry_count = 0

    while retry_count <= max_retries:
        try:
            logger.debug(f"正在获取飞书文档markdown内容，obj_token: {obj_token}, get_all: {get_all}, 重试次数: {retry_count}")
            content_url = f"https://open.feishu.cn/open-apis/docs/v1/content?content_type=markdown&doc_token={obj_token}&doc_type=docx"
            headers = {
                'Authorization': f'Bearer {access_token}'
            }

            # 设置超时时间为30秒，避免长时间等待
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(content_url, headers=headers) as response:
                    response_text = await response.text()

                    if response.status != 200:
                        # 检查是否是限流错误
                        if "request trigger frequency limit" in response_text.lower():
                            if retry_count < max_retries:
                                # 随机睡眠1000ms～2000ms后重试
                                sleep_time = random.uniform(1.0, 2.0)
                                logger.warning(f"遇到限流，等待 {sleep_time:.2f} 秒后重试，obj_token: {obj_token}, 重试次数: {retry_count + 1}")
                                await asyncio.sleep(sleep_time)
                                retry_count += 1
                                continue
                            else:
                                logger.error(f"达到最大重试次数，限流问题未解决，obj_token: {obj_token}")
                                return "飞书接口异常，请联系管理员处理下"
                        else:
                            logger.warning(f"获取文档内容失败，状态码: {response.status}, obj_token: {obj_token}, 响应内容: {response_text}")
                            return None

                    try:
                        content_result = await response.json()
                    except Exception as json_error:
                        logger.error(f"解析响应JSON失败: {json_error}, obj_token: {obj_token}, 响应内容: {response_text}")
                        return None

                    if content_result.get('code') != 0:
                        logger.warning(f"获取文档内容接口返回错误: {content_result.get('msg', '未知错误')}, obj_token: {obj_token}")
                        return None

                    content = content_result.get('data', {}).get('content', '').strip()
                    logger.debug(f"获取到文档内容，obj_token: {obj_token}, 内容长度: {len(content)} 字符")

                    if get_all:
                        return content

                    if len(content) > FILE_CONTENT_LIMIT:
                        original_length = len(content)
                        content = content[:FILE_CONTENT_LIMIT] + f"\n\n(内容已被截断, 总长度{original_length}, 截断到{FILE_CONTENT_LIMIT}，如果有需要，请使用get_feishu_doc_content_tool工具获取全部内容)"

                    return content

        except Exception as e:
            logger.exception(f"获取文档内容时发生异常: {str(e)}, obj_token: {obj_token}, 重试次数: {retry_count}")
            if retry_count >= max_retries:
                return None
            retry_count += 1
            # 异常情况下也等待一下再重试
            await asyncio.sleep(random.uniform(1.0, 2.0))

    return None


# 注册工具
tool_manager.register_as_function_tool(search_feishu_docs)
tool_manager.register_as_function_tool(get_feishu_doc_content_tool)