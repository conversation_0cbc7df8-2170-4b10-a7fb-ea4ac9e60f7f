version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: chatbi_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: xianmu619
      MYSQL_DATABASE: chatbi
      MYSQL_USER: test
      MYSQL_PASSWORD: xianmu619
    ports:
      - "3308:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password --bind-address=0.0.0.0
    networks:
      - chatbi_network

volumes:
  mysql_data:

networks:
  chatbi_network:
    driver: bridge